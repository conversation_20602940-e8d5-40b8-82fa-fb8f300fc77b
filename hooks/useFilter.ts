import { useState, useCallback } from "react";
import { FilterValues, FilterField } from "@/components/shared/filter-component";

export interface UseFilterOptions {
  initialValues?: FilterValues;
  onApply?: (values: FilterValues) => void;
  onReset?: () => void;
}

export const useFilter = (
  fields: FilterField[],
  options: UseFilterOptions = {}
) => {
  const { initialValues = {}, onApply, onReset } = options;

  // Initialize filter values based on field types
  const getInitialValues = useCallback(() => {
    const values: FilterValues = { ...initialValues };
    
    fields.forEach(field => {
      if (!(field.key in values)) {
        switch (field.type) {
          case 'multiselect':
            values[field.key] = [];
            break;
          case 'toggle':
            values[field.key] = false;
            break;
          default:
            values[field.key] = '';
            break;
        }
      }
    });
    
    return values;
  }, [fields, initialValues]);

  const [filterValues, setFilterValues] = useState<FilterValues>(getInitialValues);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const openFilter = useCallback(() => {
    setIsFilterOpen(true);
  }, []);

  const closeFilter = useCallback(() => {
    setIsFilterOpen(false);
  }, []);

  const handleFilterChange = useCallback((values: FilterValues) => {
    setFilterValues(values);
  }, []);

  const handleApply = useCallback((values: FilterValues) => {
    setFilterValues(values);
    onApply?.(values);
    closeFilter();
  }, [onApply, closeFilter]);

  const handleReset = useCallback(() => {
    const resetValues = getInitialValues();
    setFilterValues(resetValues);
    onReset?.();
  }, [getInitialValues, onReset]);

  // Check if any filters are active
  const hasActiveFilters = useCallback(() => {
    return Object.entries(filterValues).some(([key, value]) => {
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      if (typeof value === 'boolean') {
        return value;
      }
      if (typeof value === 'string') {
        return value.trim() !== '';
      }
      return false;
    });
  }, [filterValues]);

  // Get count of active filters
  const getActiveFilterCount = useCallback(() => {
    let count = 0;
    Object.entries(filterValues).forEach(([key, value]) => {
      if (Array.isArray(value) && value.length > 0) {
        count += value.length;
      } else if (typeof value === 'boolean' && value) {
        count += 1;
      } else if (typeof value === 'string' && value.trim() !== '') {
        count += 1;
      }
    });
    return count;
  }, [filterValues]);

  // Get active filter labels for display
  const getActiveFilterLabels = useCallback(() => {
    const labels: string[] = [];
    
    Object.entries(filterValues).forEach(([key, value]) => {
      const field = fields.find(f => f.key === key);
      if (!field) return;

      if (Array.isArray(value) && value.length > 0) {
        if (field.type === 'multiselect') {
          const selectedOptions = field.options.filter(opt => 
            value.includes(opt.value)
          );
          labels.push(...selectedOptions.map(opt => opt.label));
        }
      } else if (typeof value === 'boolean' && value) {
        labels.push(field.label);
      } else if (typeof value === 'string' && value.trim() !== '') {
        if (field.type === 'select') {
          const selectedOption = field.options.find(opt => opt.value === value);
          if (selectedOption) {
            labels.push(selectedOption.label);
          }
        } else if (field.type === 'time') {
          const selectedSlot = field.timeSlots.find(slot => slot.time_value === value);
          if (selectedSlot) {
            labels.push(`${field.label}: ${selectedSlot.time_label}`);
          }
        }
      }
    });

    return labels;
  }, [filterValues, fields]);

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    handleReset();
  }, [handleReset]);

  // Clear specific filter
  const clearFilter = useCallback((key: string) => {
    const field = fields.find(f => f.key === key);
    if (!field) return;

    const newValues = { ...filterValues };
    
    switch (field.type) {
      case 'multiselect':
        newValues[key] = [];
        break;
      case 'toggle':
        newValues[key] = false;
        break;
      default:
        newValues[key] = '';
        break;
    }
    
    setFilterValues(newValues);
  }, [filterValues, fields]);

  return {
    // State
    filterValues,
    isFilterOpen,
    
    // Actions
    openFilter,
    closeFilter,
    handleFilterChange,
    handleApply,
    handleReset,
    clearAllFilters,
    clearFilter,
    
    // Computed values
    hasActiveFilters: hasActiveFilters(),
    activeFilterCount: getActiveFilterCount(),
    activeFilterLabels: getActiveFilterLabels(),
    
    // Filter props (ready to spread into FilterComponent)
    filterProps: {
      isOpen: isFilterOpen,
      onClose: closeFilter,
      fields,
      values: filterValues,
      onValuesChange: handleFilterChange,
      onApply: handleApply,
      onReset: handleReset,
    },
  };
};
