import { useCallback, useMemo, useState } from "react";
import { matchSorter } from "match-sorter";
import { useEventsQuery } from "@/data/screens/events/queries/useEventsQuery";
import { format } from "date-fns";
import { useFilter } from "./useFilter";
import { FilterField } from "@/components/shared/filter-component";
import { useClientInfo } from "@/data/screens/common/queries/useClientConfig";
import { EventResponse } from "@/data/screens/events/types";
import { AvailableTimeSlot } from "@/data/screens/activities/types";

// Define time slots as constants to reduce duplication
const TIME_SLOTS: AvailableTimeSlot[] = [
  {
    time_value: "06:00",
    time_label: "6:00 AM",
    full_date_time: "",
    minimum_end_time: "",
    full_minimum_end_time: "",
    reservations_at_slot: 0,
    remarks: "",
    allow_reservations: true,
  },
  {
    time_value: "21:00",
    time_label: "9:00 PM",
    full_date_time: "",
    minimum_end_time: "",
    full_minimum_end_time: "",
    reservations_at_slot: 0,
    remarks: "",
    allow_reservations: true,
  },
];

export const useEventsWithFilter = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDate, setSelectedDate] = useState(new Date());

  const { data: clientData } = useClientInfo();

  // Define filter fields for events
  const filterFields: FilterField[] = useMemo(
    () => [
      {
        type: "multiselect",
        key: "type",
        label: "Event type",
        options: [
          { label: "Virtual", value: "virtual" },
          { label: "Live", value: "live" },
          { label: "Paid", value: "paid" },
          { label: "Free", value: "free" },
        ],
      },
      {
        type: "select",
        key: "gym_id",
        label: "Location",
        placeholder: "Select location",
        options:
          clientData?.facilities.map((facility) => ({
            label: facility.name,
            value: String(facility.id),
          })) || [],
      },
      {
        type: "time",
        key: "date",
        label: "Date",
        placeholder: "Select date",
        timeSlots: TIME_SLOTS,
      },
      {
        type: "time",
        key: "time",
        label: "Time",
        placeholder: "Select date",
        timeSlots: TIME_SLOTS,
      },
      {
        type: "toggle",
        key: "only_available_reservations",
        label: "Show only events with available reservations",
      },
    ],
    [clientData?.facilities]
  );

  // Initialize filter hook
  const {
    filterValues,
    filterProps,
    hasActiveFilters,
    activeFilterCount,
    clearAllFilters,
  } = useFilter(filterFields, {
    onApply: () => {
      // Applied event filters
    },
    onReset: () => {
      // Reset event filters
    },
  });

  // Fetch events data
  const {
    data: eventsData = [],
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useEventsQuery({
    month_year: format(selectedDate, "yyyy-MM"),
  });

  // Apply filtering
  const filteredData = useMemo(() => {
    let filtered = eventsData as EventResponse[];

    // Apply search term
    if (searchTerm) {
      filtered = matchSorter(filtered, searchTerm, {
        keys: ["name", "gym_name", "room_name", "description"],
      });
    }

    // Apply type filter
    if (
      filterValues.type &&
      Array.isArray(filterValues.type) &&
      filterValues.type.length > 0
    ) {
      filtered = filtered.filter((event) => {
        const eventTypes: string[] = [];
        if (event.is_paid) eventTypes.push("paid");
        if (!event.is_paid) eventTypes.push("free");
        if (event.class_type === "virtual") eventTypes.push("virtual");
        if (event.class_type !== "virtual") eventTypes.push("live");

        return eventTypes.some((type) =>
          (filterValues.type as string[]).includes(type)
        );
      });
    }

    // Apply facility filter
    if (filterValues.gym_id && typeof filterValues.gym_id === "string") {
      filtered = filtered.filter(
        (event) => String(event.gym_id) === filterValues.gym_id
      );
    }

    // Apply availability filter
    if (filterValues.only_available_reservations) {
      filtered = filtered.filter(
        (event) =>
          event.reservation_count < event.spots &&
          event.allow_reservation_date &&
          new Date(event.allow_reservation_date) > new Date()
      );
    }

    return filtered;
  }, [eventsData, searchTerm, filterValues]);

  const handleDateChange = useCallback((date: Date) => {
    setSelectedDate(date);
  }, []);

  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchTerm("");
  }, []);

  const handleFilterChange = useCallback(() => {
    // The filter hook handles this automatically
  }, []);

  return {
    // Data
    events: filteredData,
    isLoading,
    isRefetching,
    error,

    // UI State
    selectedDate,
    searchTerm,

    // Filter state
    filterValues,
    filterFields,
    hasActiveFilters,
    activeFilterCount,

    // Actions
    handleDateChange,
    handleSearch,
    setSearchTerm,
    clearSearch,
    refetch,
    handleFilterChange,
    clearAllFilters,

    // Filter props for components
    filterProps,
  };
};
