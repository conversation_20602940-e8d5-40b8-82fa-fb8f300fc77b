import { useCallback, useMemo, useState } from "react";
import { matchSorter } from "match-sorter";
import { useClassesQuery } from "@/data/screens/classes/queries/useClassesQuery";
import { formatDate } from "@/data/common/common.utils";
import { ClassDetailsResponse } from "@/data/screens/classes/types";
import { CategoryType } from "@/data/screens/appointments/types";
import { useCategoriesAppointments } from "@/data/screens/appointments/queries/useCategoriesAppointments";
import { useFilter } from "./useFilter";
import {
  FilterField,
  FilterValues,
} from "@/components/shared/filter-component";
import { useClientInfo } from "@/data/screens/common/queries/useClientConfig";

export const useClassesWithFilter = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTab, setSelectedTab] = useState<"classes" | "appointment">(
    "classes"
  );
  const [selectedDate, setSelectedDate] = useState(new Date());

  const { data: clientData } = useClientInfo();

  // Define filter fields for classes
  const filterFields: FilterField[] = useMemo(
    () => [
      {
        type: "multiselect",
        key: "type",
        label: "Event type",
        options: [
          { label: "Virtual", value: "virtual" },
          { label: "Live", value: "live" },
          { label: "Paid", value: "paid" },
          { label: "Free", value: "free" },
        ],
      },
      {
        type: "select",
        key: "facility",
        label: "Facility",
        placeholder: "Select facility",
        options:
          clientData?.facilities.map((facility) => ({
            label: facility.name,
            value: String(facility.id),
          })) || [],
      },
      {
        type: "multiselect",
        key: "classType",
        label: "Class Type",
        options: [
          { label: "Virtual", value: "virtual" },
          { label: "Live", value: "live" },
          { label: "Virtual & Live", value: "virtual_live" },
        ],
      },
      {
        type: "time",
        key: "startTime",
        label: "Start time",
        placeholder: "Select start time",
        timeSlots: [
          {
            time_value: "06:00",
            time_label: "6:00 AM",
            allow_reservations: true,
          },
          {
            time_value: "07:00",
            time_label: "7:00 AM",
            allow_reservations: true,
          },
          {
            time_value: "08:00",
            time_label: "8:00 AM",
            allow_reservations: true,
          },
          {
            time_value: "09:00",
            time_label: "9:00 AM",
            allow_reservations: true,
          },
          {
            time_value: "10:00",
            time_label: "10:00 AM",
            allow_reservations: true,
          },
          {
            time_value: "11:00",
            time_label: "11:00 AM",
            allow_reservations: true,
          },
          {
            time_value: "12:00",
            time_label: "12:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "13:00",
            time_label: "1:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "14:00",
            time_label: "2:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "15:00",
            time_label: "3:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "16:00",
            time_label: "4:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "17:00",
            time_label: "5:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "18:00",
            time_label: "6:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "19:00",
            time_label: "7:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "20:00",
            time_label: "8:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "21:00",
            time_label: "9:00 PM",
            allow_reservations: true,
          },
        ],
      },
      {
        type: "time",
        key: "endTime",
        label: "End time",
        placeholder: "Select end time",
        timeSlots: [
          {
            time_value: "07:00",
            time_label: "7:00 AM",
            allow_reservations: true,
          },
          {
            time_value: "08:00",
            time_label: "8:00 AM",
            allow_reservations: true,
          },
          {
            time_value: "09:00",
            time_label: "9:00 AM",
            allow_reservations: true,
          },
          {
            time_value: "10:00",
            time_label: "10:00 AM",
            allow_reservations: true,
          },
          {
            time_value: "11:00",
            time_label: "11:00 AM",
            allow_reservations: true,
          },
          {
            time_value: "12:00",
            time_label: "12:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "13:00",
            time_label: "1:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "14:00",
            time_label: "2:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "15:00",
            time_label: "3:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "16:00",
            time_label: "4:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "17:00",
            time_label: "5:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "18:00",
            time_label: "6:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "19:00",
            time_label: "7:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "20:00",
            time_label: "8:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "21:00",
            time_label: "9:00 PM",
            allow_reservations: true,
          },
          {
            time_value: "22:00",
            time_label: "10:00 PM",
            allow_reservations: true,
          },
        ],
      },
      {
        type: "toggle",
        key: "onlyAvailable",
        label: "Only show what is available",
        description: "Show only classes with available spots",
      },
      {
        type: "toggle",
        key: "onlyFavorites",
        label: "Show favorites only",
        description: "Show only your favorite classes",
      },
      {
        type: "toggle",
        key: "allowWaitlist",
        label: "Allow waitlist",
        description: "Include classes that allow waitlist",
      },
    ],
    [clientData?.facilities]
  );

  // Initialize filter hook
  const {
    filterValues,
    filterProps,
    hasActiveFilters,
    activeFilterCount,
    clearAllFilters,
  } = useFilter(filterFields, {
    onApply: (values) => {
      console.log("Applied class filters:", values);
    },
    onReset: () => {
      console.log("Reset class filters");
    },
  });

  // Build query parameters from filter values
  const queryParams = useMemo(() => {
    const params: any = {
      date: formatDate(selectedDate),
    };

    if (filterValues.facility && typeof filterValues.facility === "string") {
      params.gym_id = filterValues.facility;
    }

    return params;
  }, [selectedDate, filterValues]);

  // Fetch classes data
  const {
    data: classesData = [],
    isLoading: isClassesLoading,
    error: classesQueryError,
    refetch: refetchClasses,
    isRefetching: isClassesRefetching,
  } = useClassesQuery(queryParams);

  const {
    data: categoriesData,
    isLoading: isCategoriesLoading,
    error: categoriesQueryError,
    isRefetching: isCategoriesRefetching,
    refetch: refetchCategories,
  } = useCategoriesAppointments();

  const currentRecord = useMemo(() => {
    if (selectedTab === "classes") {
      return {
        data: classesData,
        error: classesQueryError,
        isLoading: isClassesLoading,
        isRefetching: isClassesRefetching,
      };
    }
    return {
      data: categoriesData,
      error: categoriesQueryError,
      isLoading: isCategoriesLoading,
      isRefetching: isCategoriesRefetching,
    };
  }, [
    selectedTab,
    categoriesData,
    categoriesQueryError,
    isCategoriesLoading,
    isCategoriesRefetching,
    classesData,
    classesQueryError,
    isClassesLoading,
    isClassesRefetching,
  ]);

  const filteredData = useMemo(() => {
    let filtered = currentRecord.data;

    if (!filtered) return [];

    // Apply search term
    if (searchTerm) {
      if (selectedTab === "classes") {
        filtered = matchSorter(
          (filtered as ClassDetailsResponse[]) ?? [],
          searchTerm,
          {
            keys: [
              "name",
              "room_name",
              "gym_name",
              "instructor_first_name",
              "instructor_last_name",
              "start_time",
              "category",
            ],
          }
        );
      } else {
        filtered = matchSorter((filtered as CategoryType[]) ?? [], searchTerm, {
          keys: ["name", "gym_name", "room_name", "reservations_count"],
        });
      }
    }

    // Apply filters only for classes tab
    if (selectedTab === "classes" && filtered) {
      let classFiltered = filtered as ClassDetailsResponse[];

      // Apply time filters
      if (
        filterValues.startTime &&
        typeof filterValues.startTime === "string"
      ) {
        classFiltered = classFiltered.filter(
          (cls) => cls.start_time >= filterValues.startTime
        );
      }

      if (filterValues.endTime && typeof filterValues.endTime === "string") {
        classFiltered = classFiltered.filter(
          (cls) => cls.end_time <= filterValues.endTime
        );
      }

      // Apply category filter
      if (
        filterValues.category &&
        Array.isArray(filterValues.category) &&
        filterValues.category.length > 0
      ) {
        classFiltered = classFiltered.filter((cls) => {
          const classCategory = cls.category.toLowerCase();
          return (filterValues.category as string[]).some(
            (cat) => classCategory.includes(cat) || cat.includes(classCategory)
          );
        });
      }

      // Apply class type filter
      if (
        filterValues.classType &&
        Array.isArray(filterValues.classType) &&
        filterValues.classType.length > 0
      ) {
        classFiltered = classFiltered.filter((cls) => {
          const classType = cls.class_type.toLowerCase().replace(/\s+/g, "_");
          return (filterValues.classType as string[]).includes(classType);
        });
      }

      // Apply availability filter
      if (filterValues.onlyAvailable) {
        classFiltered = classFiltered.filter(
          (cls) =>
            cls.spots_available > 0 && cls.allow_reservations && !cls.cancelled
        );
      }

      // Apply favorites filter
      if (filterValues.onlyFavorites) {
        classFiltered = classFiltered.filter((cls) => cls.is_favourite);
      }

      // Apply waitlist filter
      if (filterValues.allowWaitlist) {
        classFiltered = classFiltered.filter(
          (cls) => cls.allow_waitlist && cls.waitlist_spots_available > 0
        );
      }

      filtered = classFiltered;
    }

    return filtered;
  }, [currentRecord.data, searchTerm, selectedTab, filterValues]);

  const refetch = useCallback(() => {
    if (selectedTab === "classes") {
      return refetchClasses();
    } else {
      return refetchCategories();
    }
  }, [selectedTab, refetchClasses, refetchCategories]);

  // Handle tab change
  const handleTabChange = useCallback((tab: "classes" | "appointment") => {
    setSelectedTab(tab);
    setSearchTerm("");
  }, []);

  // Handle date change
  const handleDateChange = useCallback(
    (date: Date) => {
      setSelectedDate(date);
    },
    [setSelectedDate]
  );

  const clearSearch = useCallback(() => {
    setSearchTerm("");
  }, []);

  const handleFilterChange = useCallback((values: FilterValues) => {
    // The filter hook handles this automatically
  }, []);

  return {
    // Data
    filteredData,
    ...currentRecord,

    // UI State
    selectedTab,
    selectedDate,
    searchTerm,

    // Filter state
    filterValues,
    filterFields,
    hasActiveFilters,
    activeFilterCount,

    // Actions
    setSearchTerm,
    clearSearch,
    handleTabChange,
    handleDateChange,
    refetch,
    handleFilterChange,
    clearAllFilters,

    // Filter props for components
    filterProps,
  };
};
