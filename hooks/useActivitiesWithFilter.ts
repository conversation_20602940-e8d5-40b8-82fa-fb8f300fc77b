import { useState, useMemo, useCallback } from "react";
import { useActivitiesQuery } from "@/data/screens/activities/queries/useActivitiesQuery";
import { formatDate } from "@/data/common/common.utils";
import { matchSorter } from "match-sorter";
import { useFilter } from "./useFilter";
import { FilterField, FilterValues } from "@/components/shared/filter-component";
import { useClientInfo } from "@/data/screens/common/queries/useClientConfig";

export const useActivitiesWithFilter = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [searchTerm, setSearchTerm] = useState("");
  
  const { data: clientData } = useClientInfo();
  const formattedDate = formatDate(selectedDate);

  // Define filter fields for activities
  const filterFields: FilterField[] = useMemo(() => [
    {
      type: "multiselect",
      key: "category",
      label: "Category",
      options: [
        { label: "Fitness Center", value: "fitness_center" },
        { label: "Cardio", value: "cardio" },
        { label: "Strength Training", value: "strength" },
        { label: "Pool", value: "pool" },
        { label: "Court Sports", value: "court" },
        { label: "Studio Classes", value: "studio" },
      ],
    },
    {
      type: "select",
      key: "facility",
      label: "Facility",
      placeholder: "Select facility",
      options: clientData?.facilities.map(facility => ({
        label: facility.name,
        value: String(facility.id),
      })) || [],
    },
    {
      type: "multiselect",
      key: "status",
      label: "Status",
      options: [
        { label: "Available", value: "available" },
        { label: "Busy", value: "busy" },
        { label: "Maintenance", value: "maintenance" },
      ],
    },
    {
      type: "time",
      key: "startTime",
      label: "Start time",
      placeholder: "Select start time",
      timeSlots: [
        { time_value: "06:00", time_label: "6:00 AM", allow_reservations: true },
        { time_value: "07:00", time_label: "7:00 AM", allow_reservations: true },
        { time_value: "08:00", time_label: "8:00 AM", allow_reservations: true },
        { time_value: "09:00", time_label: "9:00 AM", allow_reservations: true },
        { time_value: "10:00", time_label: "10:00 AM", allow_reservations: true },
        { time_value: "11:00", time_label: "11:00 AM", allow_reservations: true },
        { time_value: "12:00", time_label: "12:00 PM", allow_reservations: true },
        { time_value: "13:00", time_label: "1:00 PM", allow_reservations: true },
        { time_value: "14:00", time_label: "2:00 PM", allow_reservations: true },
        { time_value: "15:00", time_label: "3:00 PM", allow_reservations: true },
        { time_value: "16:00", time_label: "4:00 PM", allow_reservations: true },
        { time_value: "17:00", time_label: "5:00 PM", allow_reservations: true },
        { time_value: "18:00", time_label: "6:00 PM", allow_reservations: true },
        { time_value: "19:00", time_label: "7:00 PM", allow_reservations: true },
        { time_value: "20:00", time_label: "8:00 PM", allow_reservations: true },
        { time_value: "21:00", time_label: "9:00 PM", allow_reservations: true },
      ],
    },
    {
      type: "time",
      key: "endTime",
      label: "End time",
      placeholder: "Select end time",
      timeSlots: [
        { time_value: "07:00", time_label: "7:00 AM", allow_reservations: true },
        { time_value: "08:00", time_label: "8:00 AM", allow_reservations: true },
        { time_value: "09:00", time_label: "9:00 AM", allow_reservations: true },
        { time_value: "10:00", time_label: "10:00 AM", allow_reservations: true },
        { time_value: "11:00", time_label: "11:00 AM", allow_reservations: true },
        { time_value: "12:00", time_label: "12:00 PM", allow_reservations: true },
        { time_value: "13:00", time_label: "1:00 PM", allow_reservations: true },
        { time_value: "14:00", time_label: "2:00 PM", allow_reservations: true },
        { time_value: "15:00", time_label: "3:00 PM", allow_reservations: true },
        { time_value: "16:00", time_label: "4:00 PM", allow_reservations: true },
        { time_value: "17:00", time_label: "5:00 PM", allow_reservations: true },
        { time_value: "18:00", time_label: "6:00 PM", allow_reservations: true },
        { time_value: "19:00", time_label: "7:00 PM", allow_reservations: true },
        { time_value: "20:00", time_label: "8:00 PM", allow_reservations: true },
        { time_value: "21:00", time_label: "9:00 PM", allow_reservations: true },
        { time_value: "22:00", time_label: "10:00 PM", allow_reservations: true },
      ],
    },
    {
      type: "toggle",
      key: "onlyAvailable",
      label: "Only show what is available",
      description: "Show only activities with available time slots",
    },
    {
      type: "toggle",
      key: "onlyFavorites",
      label: "Show favorites only",
      description: "Show only your favorite activities",
    },
  ], [clientData?.facilities]);

  // Initialize filter hook
  const {
    filterValues,
    filterProps,
    hasActiveFilters,
    activeFilterCount,
    clearAllFilters,
  } = useFilter(filterFields, {
    onApply: (values) => {
      console.log("Applied activity filters:", values);
    },
    onReset: () => {
      console.log("Reset activity filters");
    },
  });

  // Build query parameters from filter values
  const queryParams = useMemo(() => {
    const params: any = {
      date: formattedDate,
    };

    if (filterValues.facility && typeof filterValues.facility === 'string') {
      params.facility_id = filterValues.facility;
    }

    if (filterValues.category && Array.isArray(filterValues.category) && filterValues.category.length > 0) {
      // For now, we'll use the first category. In a real implementation, 
      // the API might support multiple categories
      params.category = filterValues.category[0];
    }

    if (filterValues.status && Array.isArray(filterValues.status) && filterValues.status.length > 0) {
      params.status = filterValues.status[0];
    }

    return params;
  }, [formattedDate, filterValues]);

  const { data: activities, isLoading, isRefetching, refetch } = useActivitiesQuery(queryParams);

  // Apply client-side filtering
  const filteredActivities = useMemo(() => {
    if (!activities) return [];
    
    let filtered = activities;
    
    // Apply search term
    if (searchTerm) {
      filtered = matchSorter(filtered, searchTerm, {
        keys: ["name", "gym_name", "room_name", "type"],
      });
    }

    // Apply time filters
    if (filterValues.startTime && typeof filterValues.startTime === 'string') {
      filtered = filtered.filter(activity => {
        return activity.available_time_slots.some(slot => 
          slot.time_value >= filterValues.startTime
        );
      });
    }

    if (filterValues.endTime && typeof filterValues.endTime === 'string') {
      filtered = filtered.filter(activity => {
        return activity.available_time_slots.some(slot => {
          // Calculate end time based on duration (assuming 1 hour default)
          const slotHour = parseInt(slot.time_value.split(':')[0]);
          const endHour = slotHour + 1;
          const endTime = `${endHour.toString().padStart(2, '0')}:00`;
          return endTime <= filterValues.endTime;
        });
      });
    }

    // Apply availability filter
    if (filterValues.onlyAvailable) {
      filtered = filtered.filter(activity => 
        activity.current_status === "Available" && 
        activity.available_time_slots.some(slot => slot.allow_reservations)
      );
    }

    // Apply favorites filter
    if (filterValues.onlyFavorites) {
      filtered = filtered.filter(activity => activity.is_favourite);
    }

    // Apply multiple category filter (client-side)
    if (filterValues.category && Array.isArray(filterValues.category) && filterValues.category.length > 1) {
      filtered = filtered.filter(activity => {
        const activityType = activity.type.toLowerCase().replace(/\s+/g, '_');
        return (filterValues.category as string[]).some(cat => 
          activityType.includes(cat) || cat.includes(activityType)
        );
      });
    }

    // Apply multiple status filter (client-side)
    if (filterValues.status && Array.isArray(filterValues.status) && filterValues.status.length > 1) {
      filtered = filtered.filter(activity => {
        const status = activity.current_status.toLowerCase();
        return (filterValues.status as string[]).includes(status);
      });
    }
    
    return filtered;
  }, [activities, searchTerm, filterValues]);

  const handleDateChange = useCallback((date: Date) => {
    setSelectedDate(date);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchTerm("");
  }, []);

  const handleFilterChange = useCallback((values: FilterValues) => {
    // The filter hook handles this automatically
  }, []);

  return {
    // Data
    activities: filteredActivities,
    isLoading,
    isRefetching,
    
    // UI State
    selectedDate,
    searchTerm,
    
    // Filter state
    filterValues,
    filterFields,
    hasActiveFilters,
    activeFilterCount,
    
    // Actions
    handleDateChange,
    setSearchTerm,
    clearSearch,
    refetch,
    handleFilterChange,
    clearAllFilters,
    
    // Filter props for components
    filterProps,
  };
};
