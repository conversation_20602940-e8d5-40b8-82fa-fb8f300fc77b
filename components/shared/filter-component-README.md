# Reusable Filter Component

A comprehensive, reusable filter component that matches the design specifications and can be used across different pages of the application.

## Components

### 1. FilterComponent
The main filter component that renders as an actionsheet with various filter field types.

### 2. SearchWithFilter
An enhanced search component that integrates the filter functionality with a search input.

### 3. useFilt<PERSON> Hook
A custom hook that simplifies filter state management and provides convenient utilities.

## Features

- **Multiple Field Types**: Supports multiselect, single select, time selection, and toggle fields
- **Removable Tags**: Multi-select fields show selected items as removable tags with dashed borders
- **Visual Feedback**: Filter button changes color and shows badge count when filters are active
- **Reusable**: Easily configurable for different use cases across the app
- **Consistent Design**: Matches the provided design specifications

## Field Types

### MultiSelectField
```typescript
{
  type: "multiselect",
  key: "category",
  label: "Category",
  options: [
    { label: "Yoga", value: "yoga" },
    { label: "Pilates", value: "pilates" },
  ],
}
```

### SingleSelectField
```typescript
{
  type: "select",
  key: "facility",
  label: "Facility",
  placeholder: "Select facility",
  options: [
    { label: "Main Gym", value: "main-gym" },
    { label: "Pool Area", value: "pool" },
  ],
}
```

### TimeField
```typescript
{
  type: "time",
  key: "startTime",
  label: "Start time",
  placeholder: "Select start time",
  timeSlots: [
    { time_value: "09:00", time_label: "9:00 AM", allow_reservations: true },
    { time_value: "10:00", time_label: "10:00 AM", allow_reservations: true },
  ],
}
```

### ToggleField
```typescript
{
  type: "toggle",
  key: "onlyAvailable",
  label: "Only show what is available",
  description: "Show only classes with available spots",
}
```

## Usage Examples

### Basic Usage with SearchWithFilter

```typescript
import { SearchWithFilter } from "@/components/shared/search-with-filter";
import { FilterField, FilterValues } from "@/components/shared/filter-component";

const MyComponent = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterValues, setFilterValues] = useState<FilterValues>({
    category: [],
    facility: "",
    onlyAvailable: false,
  });

  const filterFields: FilterField[] = [
    {
      type: "multiselect",
      key: "category",
      label: "Category",
      options: [
        { label: "Yoga", value: "yoga" },
        { label: "Pilates", value: "pilates" },
      ],
    },
    {
      type: "toggle",
      key: "onlyAvailable",
      label: "Only show what is available",
    },
  ];

  return (
    <SearchWithFilter
      searchTerm={searchTerm}
      onSearch={setSearchTerm}
      placeholder="Search activities..."
      filterFields={filterFields}
      filterValues={filterValues}
      onFilterChange={setFilterValues}
    />
  );
};
```

### Standalone Filter Component

```typescript
import { FilterComponent } from "@/components/shared/filter-component";

const MyComponent = () => {
  const [showFilter, setShowFilter] = useState(false);
  const [filterValues, setFilterValues] = useState<FilterValues>({});

  return (
    <>
      <Button onPress={() => setShowFilter(true)}>
        <ButtonText>Open Filter</ButtonText>
      </Button>

      <FilterComponent
        isOpen={showFilter}
        onClose={() => setShowFilter(false)}
        fields={filterFields}
        values={filterValues}
        onValuesChange={setFilterValues}
        onApply={(values) => {
          // Handle filter application
          console.log("Applied filters:", values);
        }}
        onReset={() => {
          // Handle filter reset
          console.log("Reset filters");
        }}
        title="Custom Filter Title"
      />
    </>
  );
};
```

### Using the useFilter Hook

```typescript
import { useFilter } from "@/hooks/useFilter";

const MyComponent = () => {
  const filterFields: FilterField[] = [
    // ... your filter fields
  ];

  const {
    filterProps,
    openFilter,
    hasActiveFilters,
    activeFilterCount,
    activeFilterLabels,
    clearAllFilters,
  } = useFilter(filterFields, {
    initialValues: {
      category: ["yoga"],
      onlyAvailable: true,
    },
    onApply: (values) => {
      console.log("Applied filters:", values);
    },
    onReset: () => {
      console.log("Reset filters");
    },
  });

  return (
    <>
      <Button onPress={openFilter}>
        <ButtonText>
          Filter {hasActiveFilters && `(${activeFilterCount})`}
        </ButtonText>
      </Button>

      {/* Show active filter labels */}
      {activeFilterLabels.map((label, index) => (
        <Badge key={index}>
          <BadgeText>{label}</BadgeText>
        </Badge>
      ))}

      {/* Filter Component */}
      <FilterComponent {...filterProps} />
    </>
  );
};
```

## Props

### FilterComponent Props

| Prop | Type | Description |
|------|------|-------------|
| `isOpen` | `boolean` | Controls the visibility of the filter actionsheet |
| `onClose` | `() => void` | Callback when the filter is closed |
| `fields` | `FilterField[]` | Array of filter field configurations |
| `values` | `FilterValues` | Current filter values |
| `onValuesChange` | `(values: FilterValues) => void` | Callback when filter values change |
| `onApply` | `(values: FilterValues) => void` | Callback when apply button is pressed |
| `onReset` | `() => void` | Callback when reset button is pressed |
| `title` | `string` | Optional title for the filter (default: "Filter") |

### SearchWithFilter Props

| Prop | Type | Description |
|------|------|-------------|
| `onSearch` | `(text: string) => void` | Callback for search input changes |
| `searchTerm` | `string` | Current search term |
| `placeholder` | `string` | Search input placeholder |
| `filterFields` | `FilterField[]` | Array of filter field configurations |
| `filterValues` | `FilterValues` | Current filter values |
| `onFilterChange` | `(values: FilterValues) => void` | Callback when filter values change |
| `showFilterBadge` | `boolean` | Whether to show the filter count badge |

### useFilter Hook

| Prop | Type | Description |
|------|------|-------------|
| `fields` | `FilterField[]` | Array of filter field configurations |
| `options.initialValues` | `FilterValues` | Initial filter values |
| `options.onApply` | `(values: FilterValues) => void` | Callback when filters are applied |
| `options.onReset` | `() => void` | Callback when filters are reset |

#### Hook Return Values

| Property | Type | Description |
|----------|------|-------------|
| `filterValues` | `FilterValues` | Current filter values |
| `isFilterOpen` | `boolean` | Whether the filter is open |
| `openFilter` | `() => void` | Function to open the filter |
| `closeFilter` | `() => void` | Function to close the filter |
| `handleFilterChange` | `(values: FilterValues) => void` | Function to update filter values |
| `handleApply` | `(values: FilterValues) => void` | Function to apply filters |
| `handleReset` | `() => void` | Function to reset filters |
| `clearAllFilters` | `() => void` | Function to clear all filters |
| `clearFilter` | `(key: string) => void` | Function to clear a specific filter |
| `hasActiveFilters` | `boolean` | Whether any filters are active |
| `activeFilterCount` | `number` | Number of active filters |
| `activeFilterLabels` | `string[]` | Labels of active filters |
| `filterProps` | `object` | Props ready to spread into FilterComponent |

## Styling

The component uses the existing design system with:
- Dashed borders for selected category tags (`border-dashed`)
- Brand colors (`#00BFE0` for primary actions)
- Consistent typography using `dm-sans` font family
- Proper spacing and layout using the existing UI components

## Integration

The filter component integrates seamlessly with:
- Existing `TimeSlotSelector` component for time fields
- Gluestack UI components for consistent styling
- Actionsheet pattern used throughout the app
- Badge components for visual feedback

## Customization

You can easily extend the component by:
1. Adding new field types to the `FilterField` union type
2. Creating new field components following the existing pattern
3. Customizing the styling by modifying the className props
4. Adding validation or custom logic in the field change handlers
