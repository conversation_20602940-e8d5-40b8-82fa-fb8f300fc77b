import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { FilterComponent, FilterField, FilterValues } from "./filter-component";

export const FilterComponentExampleNew = () => {
  const [filterValues, setFilterValues] = useState<FilterValues>({
    categories: [],
    facility: "",
    startTime: "",
    timeRange: [480, 1200], // 8:00 AM to 8:00 PM
    date: null,
    onlyAvailable: false,
  });

  // Example filter fields with new components
  const filterFields: FilterField[] = [
    {
      type: "multiselect",
      key: "categories",
      label: "Categories",
      placeholder: "Select categories",
      options: [
        { label: "Yoga", value: "yoga" },
        { label: "Pilates", value: "pilates" },
        { label: "Cardio", value: "cardio" },
        { label: "Strength Training", value: "strength" },
        { label: "Swimming", value: "swimming" },
      ],
    },
    {
      type: "select",
      key: "facility",
      label: "Facility",
      placeholder: "Select facility",
      options: [
        { label: "Main Gym", value: "main-gym" },
        { label: "Pool Area", value: "pool" },
        { label: "Studio A", value: "studio-a" },
        { label: "Studio B", value: "studio-b" },
      ],
    },
    {
      type: "time",
      key: "startTime",
      label: "Start time",
      placeholder: "Select start time",
      timeSlots: [
        { time_value: "06:00", time_label: "6:00 AM", allow_reservations: true },
        { time_value: "07:00", time_label: "7:00 AM", allow_reservations: true },
        { time_value: "08:00", time_label: "8:00 AM", allow_reservations: true },
        { time_value: "09:00", time_label: "9:00 AM", allow_reservations: true },
        { time_value: "10:00", time_label: "10:00 AM", allow_reservations: true },
        { time_value: "11:00", time_label: "11:00 AM", allow_reservations: true },
        { time_value: "12:00", time_label: "12:00 PM", allow_reservations: true },
        { time_value: "13:00", time_label: "1:00 PM", allow_reservations: true },
        { time_value: "14:00", time_label: "2:00 PM", allow_reservations: true },
        { time_value: "15:00", time_label: "3:00 PM", allow_reservations: true },
        { time_value: "16:00", time_label: "4:00 PM", allow_reservations: true },
        { time_value: "17:00", time_label: "5:00 PM", allow_reservations: true },
        { time_value: "18:00", time_label: "6:00 PM", allow_reservations: true },
        { time_value: "19:00", time_label: "7:00 PM", allow_reservations: true },
        { time_value: "20:00", time_label: "8:00 PM", allow_reservations: true },
      ],
    },
    {
      type: "timeRange",
      key: "timeRange",
      label: "Activities start time",
      minTime: 240, // 4:00 AM
      maxTime: 1440, // 12:00 AM (midnight)
      step: 30, // 30-minute intervals
    },
    {
      type: "date",
      key: "date",
      label: "Date",
      placeholder: "Select date",
    },
    {
      type: "toggle",
      key: "onlyAvailable",
      label: "Only show what is available",
      description: "Show only activities with available spots",
    },
  ];

  const handleFilterChange = (values: FilterValues) => {
    setFilterValues(values);
  };

  const handleApply = (values: FilterValues) => {
    console.log("Applied filters:", values);
  };

  const handleReset = () => {
    setFilterValues({
      categories: [],
      facility: "",
      startTime: "",
      timeRange: [480, 1200],
      date: null,
      onlyAvailable: false,
    });
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1 p-4" space="lg">
        <Text className="text-2xl font-dm-sans-bold text-typography-900">
          New Filter Components Example
        </Text>

        <Text className="text-base font-dm-sans-regular text-typography-600">
          This example demonstrates the new TimeRange and Date filter components:
        </Text>

        <VStack space="sm">
          <Text className="text-sm font-dm-sans-medium text-typography-700">
            • TimeRange: Draggable slider for selecting time ranges
          </Text>
          <Text className="text-sm font-dm-sans-medium text-typography-700">
            • Date: Date picker using the same component as classes page
          </Text>
        </VStack>

        <FilterComponent
          fields={filterFields}
          values={filterValues}
          onValuesChange={handleFilterChange}
          onApply={handleApply}
          onReset={handleReset}
          title="Activity Filters"
        />

        <VStack space="md" className="mt-4">
          <Text className="text-lg font-dm-sans-medium text-typography-700">
            Current Filter Values:
          </Text>
          <Text className="text-sm font-dm-sans-regular text-typography-600 bg-background-50 p-4 rounded-lg">
            {JSON.stringify(filterValues, null, 2)}
          </Text>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default FilterComponentExampleNew;
