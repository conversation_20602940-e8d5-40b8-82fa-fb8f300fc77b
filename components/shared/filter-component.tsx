import React, { useState, useEffect } from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { Box } from "@/components/ui/box";
import { Icon } from "@/components/ui/icon";
import { Switch } from "@/components/ui/switch";
import { Pressable } from "@/components/ui/pressable";
import { ScrollView } from "@/components/ui/scroll-view";
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from "@/components/ui/select";
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from "@/components/ui/actionsheet";
import {
  <PERSON>lide<PERSON>,
  <PERSON>lide<PERSON><PERSON><PERSON>,
  <PERSON>lider<PERSON>ille<PERSON><PERSON><PERSON>,
  <PERSON>lider<PERSON>humb,
} from "@/components/ui/slider";
import { X, Clock, Check, CalendarDays } from "lucide-react-native";
import { ArrowDown2 } from "iconsax-react-nativejs";
import { AvailableTimeSlot } from "@/data/screens/activities/types";

import { Setting4 } from "iconsax-react-nativejs";

import { Badge, BadgeText } from "../ui/badge";
import CalendarWidget from "./calendar-widget";

// Utility functions for time conversion
const minutesToTimeString = (minutes: number): string => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  const period = hours >= 12 ? "PM" : "AM";
  const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
  return `${displayHours}:${mins.toString().padStart(2, "0")}${period}`;
};

// Filter field types
export interface SelectOption {
  label: string;
  value: string;
}

export interface MultiSelectField {
  type: "multiselect";
  key: string;
  label: string;
  options: SelectOption[];
  placeholder?: string;
}

export interface SingleSelectField {
  type: "select";
  key: string;
  label: string;
  options: SelectOption[];
  placeholder?: string;
}

export interface TimeField {
  type: "time";
  key: string;
  label: string;
  timeSlots: AvailableTimeSlot[];
  placeholder?: string;
}

export interface TimeRangeField {
  type: "timeRange";
  key: string;
  label: string;
  minTime?: number; // in minutes from 00:00 (e.g., 240 = 4:00 AM)
  maxTime?: number; // in minutes from 00:00 (e.g., 1440 = 12:00 AM next day)
  step?: number; // step in minutes (default: 30)
}

export interface DateField {
  type: "date";
  key: string;
  label: string;
  placeholder?: string;
}

export interface ToggleField {
  type: "toggle";
  key: string;
  label: string;
  description?: string;
}

export type FilterField =
  | MultiSelectField
  | SingleSelectField
  | TimeField
  | TimeRangeField
  | DateField
  | ToggleField;

export interface FilterValues {
  [key: string]: string | string[] | boolean | [number, number] | Date | null;
}

export interface FilterComponentProps {
  fields: FilterField[];
  values: FilterValues;
  onValuesChange: (values: FilterValues) => void;
  onApply: (values: FilterValues) => void;
  onReset: () => void;
  title?: string;
}

// Removable tag component for multi-select values
const RemovableTag = ({
  label,
  onRemove,
}: {
  label: string;
  onRemove: () => void;
}) => (
  <HStack className="bg-[#E6F9FC] border-[#00BFE0] border border-dashed rounded-md px-2 py-1 mr-1 mb-1 items-center">
    <Text className="text-[#00BFE0] font-dm-sans-medium text-xs mr-1">
      {label}
    </Text>
    <Pressable onPress={onRemove}>
      <Icon as={X} size="2xs" className="text-[#00BFE0]" />
    </Pressable>
  </HStack>
);

// Custom SelectItem with checkmark for multi-select
const MultiSelectItem = ({
  option,
  isSelected,
  onToggle,
}: {
  option: SelectOption;
  isSelected: boolean;
  onToggle: () => void;
}) => (
  <Pressable
    onPress={onToggle}
    className="flex-row items-center justify-between px-4 py-3 border-b border-outline-100"
  >
    <Text className="text-typography-900 font-dm-sans-regular text-base flex-1">
      {option.label}
    </Text>
    {isSelected && (
      <Icon as={Check} size="sm" className="text-[#00BFE0] ml-2" />
    )}
  </Pressable>
);

// Multi-select field component
const MultiSelectFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: MultiSelectField;
  value: string[];
  onChange: (value: string[]) => void;
}) => {
  const selectedOptions = field.options.filter((option) =>
    value.includes(option.value)
  );

  const handleOptionToggle = (optionValue: string) => {
    const newValue = value.includes(optionValue)
      ? value.filter((v) => v !== optionValue)
      : [...value, optionValue];
    onChange(newValue);
  };

  return (
    <VStack space="sm" className="w-full">
      <Text className="text-typography-700 font-dm-sans-medium text-base">
        {field.label}
      </Text>

      {/* Use Select + Portal so the list can open. We still manually manage multi-select by toggling array values. */}
      <Select
        key={value.join(",")}
        // Keep an internal string for the API, but manage array externally.
        selectedValue=""
        onValueChange={(next) => {
          if (typeof next === "string") {
            handleOptionToggle(next);
          }
        }}
      >
        <SelectTrigger
          size="md"
          className="w-full self-stretch bg-background-50 border border-outline-200 rounded-xl px-4  h-14 min-h-14"
        >
          {/* Show chips or placeholder inside the trigger */}
          {selectedOptions.length > 0 ? (
            <HStack className="flex-1 flex-wrap">
              {selectedOptions.map((option) => (
                <RemovableTag
                  key={option.value}
                  label={option.label}
                  onRemove={() => handleOptionToggle(option.value)}
                />
              ))}
            </HStack>
          ) : (
            <Text className="text-typography-500 text-base flex-1">
              {field.placeholder || `Select ${field.label.toLowerCase()}`}
            </Text>
          )}
          <Icon as={() => <ArrowDown2 size="20" color="#9CA3AF" />} />
        </SelectTrigger>

        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView>
              {field.options.map((option) => (
                <MultiSelectItem
                  key={option.value}
                  option={option}
                  isSelected={value.includes(option.value)}
                  onToggle={() => handleOptionToggle(option.value)}
                />
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </VStack>
  );
};

/**
 * Single select field component
 */
const SingleSelectFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: SingleSelectField;
  value: string;
  onChange: (value: string) => void;
}) => (
  <VStack space="sm" className="w-full">
    <Text className="text-typography-700 font-dm-sans-medium text-base">
      {field.label}
    </Text>
    <Select selectedValue={value} onValueChange={onChange}>
      <SelectTrigger
        size="md"
        className="w-full self-stretch bg-background-50 border border-outline-200 rounded-xl px-4 py-4 h-14 min-h-14"
      >
        <SelectInput
          placeholder={
            field.placeholder || `Select ${field.label.toLowerCase()}`
          }
          className="text-typography-700 flex-1 text-base"
        />
        <Icon as={() => <ArrowDown2 size="20" color="#9CA3AF" />} />
      </SelectTrigger>
      <SelectPortal>
        <SelectBackdrop />
        <SelectContent>
          <SelectDragIndicatorWrapper>
            <SelectDragIndicator />
          </SelectDragIndicatorWrapper>
          <SelectScrollView>
            {field.options.map((option) => (
              <SelectItem
                key={option.value}
                label={option.label}
                value={option.value}
              />
            ))}
          </SelectScrollView>
        </SelectContent>
      </SelectPortal>
    </Select>
  </VStack>
);

// Time field component
const TimeFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: TimeField;
  value: string;
  onChange: (value: string) => void;
}) => (
  <VStack space="sm" className="w-full">
    <Text className="text-typography-700 font-dm-sans-medium text-base">
      {field.label}
    </Text>
    <Select selectedValue={value} onValueChange={onChange}>
      <SelectTrigger
        size="md"
        className="w-full self-stretch bg-background-50 border border-outline-200 rounded-xl px-4 py-4 h-14 min-h-14"
      >
        <SelectInput
          placeholder={
            field.placeholder || `Select ${field.label.toLowerCase()}`
          }
          className="text-typography-500 flex-1 text-base"
          value={
            value
              ? field.timeSlots.find((slot) => slot.time_value === value)
                  ?.time_label
              : ""
          }
        />
        <Icon as={() => <Clock size="20" color="#9CA3AF" />} />
      </SelectTrigger>
      <SelectPortal>
        <SelectBackdrop />
        <SelectContent>
          <SelectDragIndicatorWrapper>
            <SelectDragIndicator />
          </SelectDragIndicatorWrapper>
          <SelectScrollView>
            {field.timeSlots
              .filter((slot) => slot.allow_reservations)
              .map((slot) => (
                <SelectItem
                  key={slot.time_value}
                  label={slot.time_label}
                  value={slot.time_value}
                />
              ))}
          </SelectScrollView>
        </SelectContent>
      </SelectPortal>
    </Select>
  </VStack>
);

// Time range field component with draggable slider
const TimeRangeFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: TimeRangeField;
  value: [number, number]; // [startMinutes, endMinutes]
  onChange: (value: [number, number]) => void;
}) => {
  const minTime = field.minTime || 240; // 4:00 AM
  const maxTime = field.maxTime || 1440; // 12:00 AM (midnight)
  const step = field.step || 30;

  const [startTime, endTime] = value || [minTime, maxTime];

  const handleStartTimeChange = (newStartTime: number) => {
    onChange([newStartTime, Math.max(newStartTime + step, endTime)]);
  };

  const handleEndTimeChange = (newEndTime: number) => {
    onChange([Math.min(startTime, newEndTime - step), newEndTime]);
  };

  return (
    <VStack space="sm" className="w-full">
      <Text className="text-typography-700 font-dm-sans-medium text-base">
        {field.label}
      </Text>

      <Box className="bg-background-50 border border-outline-200 rounded-xl px-4 py-6">
        <VStack space="md">
          {/* Time display */}
          <HStack className="justify-between items-center">
            <Text className="text-typography-900 font-dm-sans-medium text-lg">
              {minutesToTimeString(startTime)} - {minutesToTimeString(endTime)}
            </Text>
          </HStack>

          {/* Dual slider */}
          <Box className="px-2">
            <VStack space="lg">
              {/* Start time slider */}
              <VStack space="xs">
                <Text className="text-typography-600 text-sm">Start time</Text>
                <Slider
                  value={startTime}
                  onChange={handleStartTimeChange}
                  minValue={minTime}
                  maxValue={endTime - step}
                  step={step}
                  className="w-full"
                >
                  <SliderTrack className="bg-background-300">
                    <SliderFilledTrack className="bg-[#00BFE0]" />
                  </SliderTrack>
                  <SliderThumb className="bg-[#00BFE0] border-2 border-white shadow-lg" />
                </Slider>
              </VStack>

              {/* End time slider */}
              <VStack space="xs">
                <Text className="text-typography-600 text-sm">End time</Text>
                <Slider
                  value={endTime}
                  onChange={handleEndTimeChange}
                  minValue={startTime + step}
                  maxValue={maxTime}
                  step={step}
                  className="w-full"
                >
                  <SliderTrack className="bg-background-300">
                    <SliderFilledTrack className="bg-[#00BFE0]" />
                  </SliderTrack>
                  <SliderThumb className="bg-[#00BFE0] border-2 border-white shadow-lg" />
                </Slider>
              </VStack>
            </VStack>
          </Box>
        </VStack>
      </Box>
    </VStack>
  );
};

// Date field component
const DateFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: DateField;
  value: Date | null;
  onChange: (value: Date | null) => void;
}) => {
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleDateSelect = (date: Date) => {
    onChange(date);
    setShowDatePicker(false);
  };

  return (
    <VStack space="sm" className="w-full">
      <Text className="text-typography-700 font-dm-sans-medium text-base">
        {field.label}
      </Text>

      <Pressable
        onPress={() => setShowDatePicker(true)}
        className="w-full bg-background-50 border border-outline-200 rounded-xl px-4 py-4 h-14 min-h-14 flex-row items-center justify-between"
      >
        <Text
          className={`flex-1 text-base ${value ? "text-typography-900" : "text-typography-500"}`}
        >
          {value
            ? value.toLocaleDateString()
            : field.placeholder || "Select date"}
        </Text>
        <Icon as={CalendarDays} size="sm" className="text-typography-500" />
      </Pressable>

      {showDatePicker && (
        <Actionsheet
          isOpen={showDatePicker}
          onClose={() => setShowDatePicker(false)}
        >
          <ActionsheetBackdrop />
          <ActionsheetContent>
            <VStack className="w-full">
              <CalendarWidget
                selectedDate={value || new Date()}
                onDateSelect={handleDateSelect}
              />
            </VStack>
          </ActionsheetContent>
        </Actionsheet>
      )}
    </VStack>
  );
};

// Toggle field component
const ToggleFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: ToggleField;
  value: boolean;
  onChange: (value: boolean) => void;
}) => (
  <VStack space="sm" className="w-full">
    <Box className="w-full bg-background-50 border border-outline-200 rounded-xl px-4 py-4">
      <HStack className="items-center justify-between w-full">
        <VStack className="flex-1">
          <Text className="text-typography-700 font-dm-sans-medium text-base">
            {field.label}
          </Text>
          {field.description && (
            <Text className="text-typography-500 font-dm-sans-regular text-sm mt-1">
              {field.description}
            </Text>
          )}
        </VStack>
        <Switch
          value={value}
          onValueChange={onChange}
          size="md"
          className="ml-3"
        />
      </HStack>
    </Box>
  </VStack>
);

export const FilterComponent = ({
  fields,
  values,
  onValuesChange,
  onApply,
  onReset,
  title = "Filter",
}: FilterComponentProps) => {
  const [localValues, setLocalValues] = useState<FilterValues>(values);

  const [isOpen, setIsOpen] = useState(false);

  const activeFilterCount = 4;

  useEffect(() => {
    if (isOpen) {
      setLocalValues(values);
    }
  }, [values, isOpen]);

  const handleFieldChange = (
    key: string,
    value: string | string[] | boolean | [number, number] | Date | null
  ) => {
    const newValues = { ...localValues, [key]: value };
    setLocalValues(newValues);
  };

  const handleApply = () => {
    onValuesChange(localValues);
    onApply(localValues);
    setIsOpen(false);
  };

  const handleReset = () => {
    const resetValues: FilterValues = {};
    fields.forEach((field) => {
      if (field.type === "multiselect") {
        resetValues[field.key] = [];
      } else if (field.type === "toggle") {
        resetValues[field.key] = false;
      } else if (field.type === "timeRange") {
        resetValues[field.key] = [field.minTime || 240, field.maxTime || 1440];
      } else if (field.type === "date") {
        resetValues[field.key] = null;
      } else {
        resetValues[field.key] = "";
      }
    });
    setLocalValues(resetValues);
    onReset();
  };

  const renderField = (field: FilterField) => {
    const value = localValues[field.key];

    switch (field.type) {
      case "multiselect":
        return (
          <MultiSelectFieldComponent
            key={field.key}
            field={field}
            value={(value as string[]) || []}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      case "select":
        return (
          <SingleSelectFieldComponent
            key={field.key}
            field={field}
            value={(value as string) || ""}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      case "time":
        return (
          <TimeFieldComponent
            key={field.key}
            field={field}
            value={(value as string) || ""}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      case "timeRange":
        return (
          <TimeRangeFieldComponent
            key={field.key}
            field={field}
            value={
              (value as [number, number]) || [
                field.minTime || 240,
                field.maxTime || 1440,
              ]
            }
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      case "date":
        return (
          <DateFieldComponent
            key={field.key}
            field={field}
            value={(value as Date) || null}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      case "toggle":
        return (
          <ToggleFieldComponent
            key={field.key}
            field={field}
            value={(value as boolean) || false}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Pressable
        onPress={() => setIsOpen(true)}
        className={`w-10 h-10 rounded-full items-center justify-center border relative ${
          activeFilterCount > 0
            ? "bg-[#00BFE0] border-[#00BFE0]"
            : "bg-background-50 border-outline-200"
        }`}
      >
        <Icon
          color={activeFilterCount > 0 ? "white" : "gray"}
          as={() => (
            <Setting4
              size="20"
              color={activeFilterCount > 0 ? "white" : "gray"}
            />
          )}
          className={
            activeFilterCount > 0 ? "text-white" : "text-typography-600"
          }
          size="md"
        />

        {/* Filter count badge */}
        {activeFilterCount > 0 && (
          <Badge
            className="absolute -top-1 -right-1 bg-red-500 min-w-5 h-5 rounded-full items-center justify-center px-1"
            variant="solid"
          >
            <BadgeText className="text-white text-xs font-dm-sans-bold">
              {activeFilterCount > 99 ? "99+" : activeFilterCount.toString()}
            </BadgeText>
          </Badge>
        )}
      </Pressable>
      {isOpen && (
        <Actionsheet isOpen={isOpen} onClose={() => setIsOpen(false)}>
          <ActionsheetBackdrop className="bg-black opacity-50" />
          <ActionsheetContent className="bg-white flex-1 max-h-[85%]">
            <ActionsheetDragIndicatorWrapper>
              <ActionsheetDragIndicator />
            </ActionsheetDragIndicatorWrapper>

            {/* Header */}
            <HStack className="items-center  self-start mb-4">
              <Pressable onPress={() => setIsOpen(false)}>
                <Icon as={X} size="lg" className="text-typography-700 mr-6" />
              </Pressable>
              <Text className="text-xl font-dm-sans-bold text-typography-900 text-left">
                {title}
              </Text>
            </HStack>

            {/* Filter Fields */}
            <ScrollView
              className="flex-1 w-full"
              showsVerticalScrollIndicator={false}
              contentContainerClassName="flex-grow"
            >
              <VStack space="lg" className=" w-full">
                {fields.map(renderField)}
              </VStack>
            </ScrollView>

            {/* Footer Actions */}
            <HStack
              space="md"
              className="px-6 py-6 border-t border-outline-100 bg-white"
            >
              <Button
                variant="outline"
                onPress={handleReset}
                className="flex-1 border-typography-300 bg-transparent rounded-full h-12"
              >
                <ButtonText className="text-typography-600 font-dm-sans-medium text-base">
                  Reset all
                </ButtonText>
              </Button>
              <Button
                onPress={handleApply}
                className="flex-1 bg-[#00BFE0] rounded-full h-12"
              >
                <ButtonText className="text-black font-dm-sans-medium text-base">
                  Apply
                </ButtonText>
              </Button>
            </HStack>
          </ActionsheetContent>
        </Actionsheet>
      )}
    </>
  );
};

FilterComponent.displayName = "FilterComponent";
export default FilterComponent;
