# Filter Component - New Features

This document describes the new TimeRange and Date field components added to the FilterComponent.

## New Field Types

### TimeRangeField

A draggable time range selector that allows users to select start and end times using sliders.

```typescript
{
  type: "timeRange",
  key: "activityTime",
  label: "Activities start time",
  minTime: 240,    // 4:00 AM (in minutes from 00:00)
  maxTime: 1440,   // 12:00 AM next day (in minutes from 00:00)
  step: 30,        // 30-minute intervals
}
```

**Props:**
- `minTime`: Minimum time in minutes from 00:00 (default: 240 = 4:00 AM)
- `maxTime`: Maximum time in minutes from 00:00 (default: 1440 = 12:00 AM)
- `step`: Step interval in minutes (default: 30)

**Value:** `[number, number]` - Array of start and end times in minutes from 00:00

**Features:**
- Dual sliders for start and end time selection
- Real-time time display (e.g., "4:00AM - 12:00PM")
- Automatic constraint handling (start time cannot exceed end time)
- Customizable time ranges and step intervals

### DateField

A date picker that uses the same HorizontalDatePicker component from the classes page.

```typescript
{
  type: "date",
  key: "selectedDate",
  label: "Date",
  placeholder: "Select date",
}
```

**Props:**
- `placeholder`: Placeholder text when no date is selected

**Value:** `Date | null` - Selected date or null if none selected

**Features:**
- Integrates with existing HorizontalDatePicker component
- Consistent UI with classes page date selection
- Shows selected date in localized format
- Calendar icon for visual clarity

## Usage Example

```typescript
import { FilterComponent, FilterField, FilterValues } from "./filter-component";

const filterFields: FilterField[] = [
  // Time range selector
  {
    type: "timeRange",
    key: "timeRange",
    label: "Activities start time",
    minTime: 240,  // 4:00 AM
    maxTime: 1440, // 12:00 AM
    step: 30,      // 30-minute steps
  },
  
  // Date selector
  {
    type: "date",
    key: "date",
    label: "Date",
    placeholder: "Select date",
  },
  
  // Other existing field types...
];

const [filterValues, setFilterValues] = useState<FilterValues>({
  timeRange: [480, 1200], // 8:00 AM to 8:00 PM
  date: null,
  // other values...
});
```

## Time Conversion Utilities

The component includes utility functions for converting between minutes and time strings:

- `minutesToTimeString(minutes: number)`: Converts minutes to "H:MMAM/PM" format
- `timeStringToMinutes(timeString: string)`: Converts time string back to minutes

## Integration Notes

1. **FilterValues Type**: Updated to support the new field types:
   ```typescript
   export interface FilterValues {
     [key: string]: string | string[] | boolean | [number, number] | Date | null;
   }
   ```

2. **Reset Behavior**: 
   - TimeRange fields reset to their min/max values
   - Date fields reset to null

3. **Validation**: The TimeRange component automatically handles constraints to ensure start time doesn't exceed end time.

## Visual Design

- **TimeRange**: Matches the design shown in the provided image with cyan-colored sliders and time display
- **Date**: Uses the existing HorizontalDatePicker for consistency with the classes page
- Both components follow the same styling patterns as other filter fields
