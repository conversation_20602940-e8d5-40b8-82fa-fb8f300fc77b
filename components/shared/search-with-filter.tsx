import React from "react";
import { SearchNormal1 } from "iconsax-react-nativejs";
import { HStack } from "../ui/hstack";
import { Input, InputField, InputIcon, InputSlot } from "../ui/input";

export interface SearchWithFilterProps {
  onSearch?: (text: string) => void;
  searchTerm?: string;
  placeholder?: string;
  showFilterBadge?: boolean;
  filter?: React.ReactNode;
}

export const SearchWithFilter = ({
  onSearch,
  searchTerm,
  placeholder = "Search",
  filter,
}: SearchWithFilterProps) => {
  return (
    <>
      <HStack className="gap-2 pl-4 pr-4">
        <Input className="h-10 flex-1 rounded-lg bg-white border" size="md">
          <InputSlot className="pl-4">
            <InputIcon
              as={() => <SearchNormal1 size="20" color="gray" />}
              className="text-typography-400"
              size="md"
              color="gray"
            />
          </InputSlot>
          <InputField
            placeholder={placeholder}
            className="placeholder:text-typography-400 focus:border-none"
            onChangeText={onSearch}
            value={searchTerm}
          />
        </Input>
        {filter}
      </HStack>
    </>
  );
};

SearchWithFilter.displayName = "SearchWithFilter";
