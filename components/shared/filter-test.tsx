import React, { useState } from "react";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { SafeAreaView } from "react-native-safe-area-context";
import { FilterComponent, FilterField, FilterValues } from "./filter-component";

// Simple test component to verify filter functionality
export const FilterTest = () => {
  const [showFilter, setShowFilter] = useState(false);
  const [filterValues, setFilterValues] = useState<FilterValues>({
    category: ["yoga", "pilates"], // Pre-select some items to test the design
    facility: "",
    startTime: "",
    endTime: "",
    onlyAvailable: false,
  });

  const filterFields: FilterField[] = [
    {
      type: "multiselect",
      key: "category",
      label: "Category",
      options: [
        { label: "Yoga", value: "yoga" },
        { label: "Pilates", value: "pilates" },
        { label: "Cardio", value: "cardio" },
        { label: "Strength", value: "strength" },
      ],
    },
    {
      type: "select",
      key: "facility",
      label: "Facility",
      placeholder: "Select facility",
      options: [
        { label: "Main Gym", value: "main-gym" },
        { label: "Pool Area", value: "pool" },
        { label: "Studio A", value: "studio-a" },
      ],
    },
    {
      type: "time",
      key: "startTime",
      label: "Start time",
      placeholder: "Select start time",
      timeSlots: [
        {
          time_value: "09:00",
          time_label: "9:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "10:00",
          time_label: "10:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "11:00",
          time_label: "11:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "12:00",
          time_label: "12:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "13:00",
          time_label: "1:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "14:00",
          time_label: "2:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "15:00",
          time_label: "3:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "16:00",
          time_label: "4:00 PM",
          allow_reservations: true,
        },
      ],
    },
    {
      type: "time",
      key: "endTime",
      label: "End time",
      placeholder: "Select end time",
      timeSlots: [
        {
          time_value: "10:00",
          time_label: "10:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "11:00",
          time_label: "11:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "12:00",
          time_label: "12:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "13:00",
          time_label: "1:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "14:00",
          time_label: "2:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "15:00",
          time_label: "3:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "16:00",
          time_label: "4:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "17:00",
          time_label: "5:00 PM",
          allow_reservations: true,
        },
      ],
    },
    {
      type: "toggle",
      key: "onlyAvailable",
      label: "Only show what is available",
      description: "Show only items with available spots",
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1 p-4" space="lg">
        <Text className="text-2xl font-dm-sans-bold text-typography-900">
          Filter Test
        </Text>

        <Button onPress={() => setShowFilter(true)}>
          <ButtonText>Open Filter</ButtonText>
        </Button>

        <VStack space="md">
          <Text className="text-lg font-dm-sans-medium text-typography-700">
            Current Filter Values:
          </Text>
          <Text className="text-sm font-dm-sans-regular text-typography-600 bg-background-50 p-4 rounded-lg">
            {JSON.stringify(filterValues, null, 2)}
          </Text>
        </VStack>

        <FilterComponent
          isOpen={showFilter}
          onClose={() => setShowFilter(false)}
          fields={filterFields}
          values={filterValues}
          onValuesChange={setFilterValues}
          onApply={(values) => {
            console.log("Applied filters:", values);
            setFilterValues(values);
          }}
          onReset={() => {
            console.log("Reset filters");
            setFilterValues({
              category: [],
              facility: "",
              onlyAvailable: false,
            });
          }}
          title="Test Filter"
        />
      </VStack>
    </SafeAreaView>
  );
};
