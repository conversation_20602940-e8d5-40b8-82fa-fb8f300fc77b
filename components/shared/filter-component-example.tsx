import React, { useState } from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { Badge, BadgeText } from "@/components/ui/badge";
import { SafeAreaView } from "react-native-safe-area-context";
import { SearchWithFilter } from "./search-with-filter";
import { SearchInput } from "./search";
import { FilterComponent, FilterField, FilterValues } from "./filter-component";
import { useFilter } from "@/hooks/useFilter";

// Example usage of the FilterComponent
export const FilterComponentExample = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterValues, setFilterValues] = useState<FilterValues>({
    category: [],
    facility: "",
    startTime: "",
    endTime: "",
    onlyAvailable: false,
  });

  // Example filter fields configuration
  const filterFields: FilterField[] = [
    {
      type: "multiselect",
      key: "category",
      label: "Category",
      options: [
        { label: "Yoga", value: "yoga" },
        { label: "Pilates", value: "pilates" },
        { label: "Cardio", value: "cardio" },
        { label: "Strength", value: "strength" },
        { label: "Swimming", value: "swimming" },
        { label: "Dance", value: "dance" },
      ],
    },
    {
      type: "select",
      key: "facility",
      label: "Facility",
      placeholder: "Select facility",
      options: [
        { label: "Main Gym", value: "main-gym" },
        { label: "Pool Area", value: "pool" },
        { label: "Studio A", value: "studio-a" },
        { label: "Studio B", value: "studio-b" },
        { label: "Outdoor Court", value: "outdoor" },
      ],
    },
    {
      type: "time",
      key: "startTime",
      label: "Start time",
      placeholder: "Select start time",
      timeSlots: [
        {
          time_value: "06:00",
          time_label: "6:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "07:00",
          time_label: "7:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "08:00",
          time_label: "8:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "09:00",
          time_label: "9:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "10:00",
          time_label: "10:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "11:00",
          time_label: "11:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "12:00",
          time_label: "12:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "13:00",
          time_label: "1:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "14:00",
          time_label: "2:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "15:00",
          time_label: "3:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "16:00",
          time_label: "4:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "17:00",
          time_label: "5:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "18:00",
          time_label: "6:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "19:00",
          time_label: "7:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "20:00",
          time_label: "8:00 PM",
          allow_reservations: true,
        },
      ],
    },
    {
      type: "time",
      key: "endTime",
      label: "End time",
      placeholder: "Select end time",
      timeSlots: [
        {
          time_value: "07:00",
          time_label: "7:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "08:00",
          time_label: "8:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "09:00",
          time_label: "9:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "10:00",
          time_label: "10:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "11:00",
          time_label: "11:00 AM",
          allow_reservations: true,
        },
        {
          time_value: "12:00",
          time_label: "12:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "13:00",
          time_label: "1:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "14:00",
          time_label: "2:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "15:00",
          time_label: "3:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "16:00",
          time_label: "4:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "17:00",
          time_label: "5:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "18:00",
          time_label: "6:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "19:00",
          time_label: "7:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "20:00",
          time_label: "8:00 PM",
          allow_reservations: true,
        },
        {
          time_value: "21:00",
          time_label: "9:00 PM",
          allow_reservations: true,
        },
      ],
    },
    {
      type: "toggle",
      key: "onlyAvailable",
      label: "Only show what is available",
      description: "Show only classes with available spots",
    },
  ];

  const handleFilterChange = (values: FilterValues) => {
    setFilterValues(values);
    console.log("Filter values changed:", values);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1 p-4" space="lg">
        <Text className="text-2xl font-dm-sans-bold text-typography-900">
          Filter Component Example
        </Text>

        <SearchWithFilter
          searchTerm={searchTerm}
          onSearch={setSearchTerm}
          placeholder="Search activities..."
          filterFields={filterFields}
          filterValues={filterValues}
          onFilterChange={handleFilterChange}
        />

        <VStack space="md" className="mt-4">
          <Text className="text-lg font-dm-sans-medium text-typography-700">
            Current Filter Values:
          </Text>
          <Text className="text-sm font-dm-sans-regular text-typography-600 bg-background-50 p-4 rounded-lg">
            {JSON.stringify(filterValues, null, 2)}
          </Text>
        </VStack>

        <VStack space="md">
          <Text className="text-lg font-dm-sans-medium text-typography-700">
            Search Term:
          </Text>
          <Text className="text-sm font-dm-sans-regular text-typography-600 bg-background-50 p-4 rounded-lg">
            {searchTerm || "No search term"}
          </Text>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

// Alternative usage with standalone FilterComponent
export const StandaloneFilterExample = () => {
  const [showFilter, setShowFilter] = useState(false);
  const [filterValues, setFilterValues] = useState<FilterValues>({
    category: ["yoga", "pilates"],
    facility: "main-gym",
    startTime: "09:00",
    endTime: "17:00",
    onlyAvailable: true,
  });

  const filterFields: FilterField[] = [
    {
      type: "multiselect",
      key: "category",
      label: "Category",
      options: [
        { label: "Yoga", value: "yoga" },
        { label: "Pilates", value: "pilates" },
        { label: "Cardio", value: "cardio" },
        { label: "Strength", value: "strength" },
      ],
    },
    {
      type: "select",
      key: "facility",
      label: "Facility",
      options: [
        { label: "Main Gym", value: "main-gym" },
        { label: "Pool Area", value: "pool" },
        { label: "Studio A", value: "studio-a" },
      ],
    },
    {
      type: "toggle",
      key: "onlyAvailable",
      label: "Only show what is available",
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1 p-4" space="lg">
        <Text className="text-2xl font-dm-sans-bold text-typography-900">
          Standalone Filter Example
        </Text>

        {/* Your existing search component */}
        <SearchInput placeholder="Search..." onSearch={() => {}} />

        {/* Custom filter trigger button */}
        <Button onPress={() => setShowFilter(true)}>
          <ButtonText>Open Filter</ButtonText>
        </Button>

        {/* Standalone Filter Component */}
        <FilterComponent
          isOpen={showFilter}
          onClose={() => setShowFilter(false)}
          fields={filterFields}
          values={filterValues}
          onValuesChange={setFilterValues}
          onApply={(values) => {
            console.log("Applied filters:", values);
          }}
          onReset={() => {
            console.log("Reset filters");
          }}
          title="Custom Filter Title"
        />
      </VStack>
    </SafeAreaView>
  );
};

// Example using the useFilter hook
export const FilterHookExample = () => {
  const filterFields: FilterField[] = [
    {
      type: "multiselect",
      key: "category",
      label: "Category",
      options: [
        { label: "Yoga", value: "yoga" },
        { label: "Pilates", value: "pilates" },
        { label: "Cardio", value: "cardio" },
        { label: "Strength", value: "strength" },
      ],
    },
    {
      type: "select",
      key: "facility",
      label: "Facility",
      options: [
        { label: "Main Gym", value: "main-gym" },
        { label: "Pool Area", value: "pool" },
        { label: "Studio A", value: "studio-a" },
      ],
    },
    {
      type: "toggle",
      key: "onlyAvailable",
      label: "Only show what is available",
    },
  ];

  const {
    filterProps,
    openFilter,
    hasActiveFilters,
    activeFilterCount,
    activeFilterLabels,
    clearAllFilters,
  } = useFilter(filterFields, {
    initialValues: {
      category: ["yoga", "pilates"],
      facility: "main-gym",
      onlyAvailable: true,
    },
    onApply: (values) => {
      console.log("Applied filters:", values);
    },
    onReset: () => {
      console.log("Reset filters");
    },
  });

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1 p-4" space="lg">
        <Text className="text-2xl font-dm-sans-bold text-typography-900">
          Filter Hook Example
        </Text>

        {/* Your existing search component */}
        <SearchInput placeholder="Search..." onSearch={() => {}} />

        {/* Custom filter trigger button with badge */}
        <HStack space="md">
          <Button onPress={openFilter} className="flex-1">
            <ButtonText>
              Open Filter {hasActiveFilters && `(${activeFilterCount})`}
            </ButtonText>
          </Button>

          {hasActiveFilters && (
            <Button variant="outline" onPress={clearAllFilters}>
              <ButtonText>Clear All</ButtonText>
            </Button>
          )}
        </HStack>

        {/* Show active filter labels */}
        {activeFilterLabels.length > 0 && (
          <VStack space="sm">
            <Text className="text-lg font-dm-sans-medium text-typography-700">
              Active Filters:
            </Text>
            <HStack space="sm" className="flex-wrap">
              {activeFilterLabels.map((label, index) => (
                <Badge key={index} className="bg-[#E6F9FC] border-[#00BFE0]">
                  <BadgeText className="text-[#00BFE0]">{label}</BadgeText>
                </Badge>
              ))}
            </HStack>
          </VStack>
        )}

        {/* Filter Component using the hook */}
        <FilterComponent {...filterProps} title="Custom Filter Title" />
      </VStack>
    </SafeAreaView>
  );
};
