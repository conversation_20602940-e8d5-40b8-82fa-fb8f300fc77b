import React, { useState } from "react";
import { TouchableOpacity } from "react-native";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Box } from "@/components/ui/box";
import { Heart, ChevronDown, ChevronUp } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import { Image } from "@/components/ui/image";
import { truncateText } from "@/utils/common";
import {
  getInitials,
  getRandomColorForInitials,
} from "@/data/common/common.utils";
import { ActivityEquipment } from "@/data/screens/activities/types";
import { StatusButton } from "@/components/shared/status-button";
import { DurationSelector } from "@/components/shared/duration-selector";
import { TimeSlotSelector } from "@/components/shared/time-slot-selector";
import { useForm } from "@tanstack/react-form";
import {
  useReserveActivity,
  useCancelActivityReservation,
} from "@/data/screens/activities/mutations/useReserveActivity";
import {
  StatusActionSheet,
  ActionButton,
} from "@/components/shared/status-actionsheet";

interface ActivityEquipmentCardProps {
  equipment: ActivityEquipment;
  selectedDate: Date;
  onFavoritePress?: () => void;
}

const getEquipmentStatus = (equipment: ActivityEquipment) => {
  // Check if equipment has available slots
  const hasAvailableSlots =
    equipment.available_time_slots && equipment.available_time_slots.length > 0;

  // Simulate reserved status (in real app, this would come from user's reservations)
  const isReserved = equipment.name === "Treadmill" && equipment.id === 1546;
  const isStationary =
    equipment.name === "Stationary Bike" && equipment.current_status === "Full";

  if (isReserved) {
    return {
      type: "reserved",
      statusText: "Reserved",
      statusColor: "text-green-600",
      statusBg: "bg-green-100",
      availabilityText: null,
      availabilityColor: null,
      buttonVariant: "cancel_reservation" as const,
      buttonText: "Cancel reservation",
      buttonDisabled: false,
      showExpandButton: false,
    };
  }

  if (hasAvailableSlots) {
    const nextSlot = equipment.available_time_slots[0];
    return {
      type: "available",
      statusText: null,
      statusColor: null,
      statusBg: null,
      availabilityText: `Available @ ${nextSlot.time_label}`,
      availabilityColor: "text-[#00BFE0]",
      buttonVariant: "reserve" as const,
      buttonText: "Reserve",
      buttonDisabled: false,
      showExpandButton: true,
    };
  }

  if (equipment.current_status === "Full" || isStationary) {
    return {
      type: "full",
      statusText: "Full",
      statusColor: "text-gray-600",
      statusBg: "bg-gray-100",
      availabilityText: null,
      availabilityColor: null,
      buttonVariant: "event_full" as const,
      buttonText: "Full",
      buttonDisabled: true,
      showExpandButton: false,
    };
  }

  // For equipment with next available time
  if (equipment.next_available_time_slot) {
    return {
      type: "next_available",
      statusText: null,
      statusColor: null,
      statusBg: null,
      availabilityText: `Next available: ${equipment.next_available_time_slot.time_label}`,
      availabilityColor: "text-[#00BFE0]",
      buttonVariant: "event_cancelled" as const,
      buttonText: "Not available",
      buttonDisabled: true,
      showExpandButton: false,
    };
  }

  return {
    type: "unavailable",
    statusText: null,
    statusColor: null,
    statusBg: null,
    availabilityText: null,
    availabilityColor: null,
    buttonVariant: "event_cancelled" as const,
    buttonText: "Not available",
    buttonDisabled: true,
    showExpandButton: false,
  };
};

const getActivityActions = (equipment: ActivityEquipment): ActionButton[] => [
  {
    label: "Make another reservation",
    onPress: () => {},
    variant: "primary",
  },
  {
    label: "Add to calendar",
    onPress: () => {},
    variant: "secondary",
  },
  {
    label: "Share with friends",
    onPress: () => {},
    variant: "outline",
  },
];

export const ActivityEquipmentCard = ({
  equipment,
  selectedDate,
  onFavoritePress,
}: ActivityEquipmentCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showActionSheet, setShowActionSheet] = useState(false);
  const [actionSheetError, setActionSheetError] = useState<Error | null>(null);

  const { mutate: reserveActivity, isPending: isReserving } =
    useReserveActivity();
  const { mutate: cancelReservation, isPending: isCancelling } =
    useCancelActivityReservation();

  const statusConfig = getEquipmentStatus(equipment);
  const isPending = isReserving || isCancelling;

  const form = useForm({
    defaultValues: {
      duration: "",
      startTime: "",
    },
    onSubmit: async ({ value }) => {
      if (!value.duration || !value.startTime) return;

      reserveActivity(
        {
          equipment_id: equipment.id,
          start_time: value.startTime,
          duration: parseInt(value.duration),
          facility_id: equipment.gym_id,
        },
        {
          onSuccess: () => {
            setActionSheetError(null);
            setShowActionSheet(true);
            setIsExpanded(false);
            form.reset();
          },
          onError: (error) => {
            setActionSheetError(error);
            setShowActionSheet(true);
          },
        }
      );
    },
  });

  const handleReserve = () => {
    if (statusConfig.showExpandButton) {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <VStack
      space="sm"
      className="bg-white rounded-2xl p-4 border border-background-200 mb-2"
    >
      <HStack space="sm" className="items-start">
        {equipment.image_url ? (
          <Image
            source={{ uri: equipment.image_url }}
            className="w-12 h-12 rounded-lg"
            alt={equipment.name}
          />
        ) : (
          <Box
            className="w-12 h-12 rounded-lg items-center justify-center"
            style={{
              backgroundColor: getRandomColorForInitials(equipment.name),
            }}
          >
            <Text className="text-sm font-dm-sans-bold text-white">
              {getInitials(equipment.name)}
            </Text>
          </Box>
        )}

        <VStack className="flex-1" space="xs">
          <HStack className="items-center justify-between">
            <HStack className="items-center flex-1" space="sm">
              <Text
                className="text-[#00697B] font-dm-sans-bold text-base"
                numberOfLines={2}
              >
                {truncateText(equipment.name, 20)}
              </Text>
              {statusConfig.statusText && (
                <Box
                  className={`px-2 py-1 rounded-md ${statusConfig.statusBg}`}
                >
                  <Text
                    className={`text-xs font-dm-sans-medium ${statusConfig.statusColor}`}
                  >
                    {statusConfig.statusText}
                  </Text>
                </Box>
              )}
            </HStack>
            <TouchableOpacity
              className="ml-2"
              onPress={(e) => {
                e.stopPropagation();
                onFavoritePress?.();
              }}
            >
              <Icon
                as={Heart}
                size="sm"
                className={
                  equipment.is_favourite
                    ? "text-error-500 fill-error-500"
                    : "text-typography-400"
                }
              />
            </TouchableOpacity>
          </HStack>

          <HStack className="items-center" space="xs">
            {statusConfig.availabilityText && (
              <Text
                className={`text-sm font-dm-sans-regular ${statusConfig.availabilityColor}`}
              >
                {statusConfig.availabilityText}
              </Text>
            )}
          </HStack>
        </VStack>
      </HStack>

      <HStack className="justify-between items-center">
        <HStack className="flex-1 items-center" space="sm">
          <Text className="text-xs font-dm-sans-regular text-typography-600">
            {truncateText(equipment.gym_name, 25)}
          </Text>
          {statusConfig.showExpandButton && (
            <TouchableOpacity onPress={() => setIsExpanded(!isExpanded)}>
              <Icon
                as={isExpanded ? ChevronUp : ChevronDown}
                size="sm"
                className="text-typography-600"
              />
            </TouchableOpacity>
          )}
        </HStack>

        {!isExpanded && (
          <StatusButton
            variant={statusConfig.buttonVariant}
            text={statusConfig.buttonText}
            disabled={statusConfig.buttonDisabled}
            isLoading={isPending}
            size="sm"
            onPress={handleReserve}
          />
        )}
      </HStack>

      {isExpanded && statusConfig.showExpandButton && (
        <VStack space="md" className="mt-4 pt-4 border-t border-background-200">
          <form.Field name="startTime">
            {(field) => (
              <TimeSlotSelector
                value={field.state.value}
                onChange={field.handleChange}
                timeSlots={equipment.available_time_slots || []}
                placeholder="02:00PM"
              />
            )}
          </form.Field>

          <form.Field name="duration">
            {(field) => (
              <DurationSelector
                value={field.state.value}
                onChange={field.handleChange}
                durations={equipment.durations || []}
                placeholder="30 minutes"
              />
            )}
          </form.Field>

          <form.Subscribe
            selector={(state) => [
              state.values.duration,
              state.values.startTime,
            ]}
          >
            {([duration, startTime]) => (
              <StatusButton
                variant="reserve"
                text="Reserve"
                disabled={!duration || !startTime}
                isLoading={isPending}
                size="sm"
                onPress={form.handleSubmit}
              />
            )}
          </form.Subscribe>
        </VStack>
      )}

      <StatusActionSheet
        isOpen={showActionSheet}
        onClose={() => setShowActionSheet(false)}
        status={actionSheetError ? "error" : "success"}
        title={actionSheetError ? "Error occurred" : "Reservation made"}
        description={
          actionSheetError?.message ??
          "Your activity reservation was successful!"
        }
        actions={actionSheetError ? [] : getActivityActions(equipment)}
      />
    </VStack>
  );
};
