import { api } from "@/lib/api";
import { EventsApiResponse } from "../screens/events/types";

export const fetchEventsByDate = async ({
  month_year,
}: {
  month_year: string;
}) => {
  try {
    const urlParams = new URLSearchParams({
      month_year,
    }).toString();

    const response = await api
      .get<EventsApiResponse>(`events/reservations/list?${urlParams}`)
      .json();

    return response?.events || [];
  } catch (err) {
    throw new Error("Could not fetch events", err as Error);
  }
};
