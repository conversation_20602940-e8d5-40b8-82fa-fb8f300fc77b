import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { VStack } from "@/components/ui/vstack";
import { EventsHeader } from "@/components/screens/events/events-header";
import { SearchWithFilter } from "@/components/shared/search-with-filter";
import { EventsList } from "@/components/screens/events/events-list";
import { useEventsWithFilter } from "@/hooks/useEventsWithFilter";
import { HorizontalDatePicker } from "@/components/shared/horizontal-date-picker";
import { FilterComponent } from "@/components/shared/filter-component";
import { noop } from "lodash/fp";

export const Events = () => {
  const {
    events,
    isLoading,
    isRefetching,
    selectedDate,
    searchTerm,
    filterFields,
    filterValues,
    handleDateChange,
    clearSearch,
    refetch,
    setSearchTerm,
    handleFilterChange,
  } = useEventsWithFilter();

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <EventsHeader />

        <VStack space="md" className="pb-4">
          <VStack>
            <HorizontalDatePicker
              selectedDate={selectedDate}
              onDateSelect={handleDateChange}
              config={{
                mode: "months",
              }}
            />
            <SearchWithFilter
              onSearch={setSearchTerm}
              searchTerm={searchTerm}
              placeholder="Search events"
              filter={
                <FilterComponent
                  fields={filterFields}
                  values={filterValues}
                  onValuesChange={handleFilterChange}
                  onApply={handleFilterChange}
                  onReset={noop}
                />
              }
            />
          </VStack>

          <VStack
            space="sm"
            className={events?.length ? "px-4 bg-gray-100" : ""}
          >
            <EventsList
              events={events}
              isLoading={isLoading}
              isRefreshing={isRefetching}
              searchTerm={searchTerm}
              selectedDate={selectedDate}
              onRefresh={refetch}
              onClearSearch={clearSearch}
            />
          </VStack>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default Events;
