import React from "react";
import { VStack } from "@/components/ui/vstack";
import { SafeAreaView } from "react-native-safe-area-context";
import { ClassesHeader } from "@/components/screens/classes/classes-header";
import { ClassesTabs } from "@/components/screens/classes/classes-tabs";
import { useClassesWithFilter } from "@/hooks/useClassesWithFilter";
import { ClassesList } from "@/components/classes/classes-list";
import { SearchWithFilter } from "@/components/shared/search-with-filter";
import { HorizontalDatePicker } from "@/components/shared/horizontal-date-picker";

export const Classes = () => {
  const {
    selectedTab,
    selectedDate,
    searchTerm,
    refetch,
    isLoading,
    isRefetching,
    handleTabChange,
    handleDateChange,
    setSearchTerm,
    clearSearch,
    filteredData,
    filterFields,
    filterValues,
    handleFilterChange,
  } = useClassesWithFilter();

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <ClassesHeader />
        <ClassesTabs selectedTab={selectedTab} onTabChange={handleTabChange} />
        <VStack space="md" className="pb-4">
          {selectedTab === "classes" && (
            <VStack>
              <HorizontalDatePicker
                selectedDate={selectedDate}
                onDateSelect={handleDateChange}
              />
              <SearchWithFilter
                onSearch={setSearchTerm}
                searchTerm={searchTerm}
                placeholder="Search classes"
                filterFields={filterFields}
                filterValues={filterValues}
                onFilterChange={handleFilterChange}
              />
            </VStack>
          )}

          <VStack
            space="sm"
            className={filteredData?.length ? "px-4 bg-gray-100" : ""}
          >
            <ClassesList
              selectedFacility={filterValues.facility as string}
              data={filteredData}
              selectedTab={selectedTab}
              isLoading={isLoading}
              isRefreshing={isRefetching}
              searchTerm={searchTerm}
              contentBottomPadding={100}
              onRefresh={refetch}
              onClearSearch={clearSearch}
              selectedDate={selectedDate}
            />
          </VStack>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default Classes;
